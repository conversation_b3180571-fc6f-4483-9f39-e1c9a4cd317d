{"name": "server", "version": "1.0.0", "description": "Server for 'The Salty Devs' Blog app", "main": "server.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "predev": "npx prisma generate", "dev": "nodemon server.js", "prisma:generate": "npx prisma generate"}, "repository": {"type": "git", "url": "git+https://github.com/anshoolp-endure/The-Salty-Devs.git"}, "author": "<PERSON><PERSON><PERSON> and Rhythm Naik", "license": "ISC", "bugs": {"url": "https://github.com/anshoolp-endure/The-Salty-Devs/issues"}, "homepage": "https://github.com/anshoolp-endure/The-Salty-Devs#readme", "dependencies": {"@prisma/client": "^6.14.0", "express": "^5.1.0", "nodemon": "^3.1.10", "prisma": "^6.14.0", "the-salty-devs": "file:.."}}